[22m
10:16:12.285 [info] Cleared entire schema cache
[0m[36m
10:16:12.441 [debug] Schema cache miss for Elixir.Drops.Relation.Repos.Sqlite.users (not cached: {:file_read, :enoent})
[0m[22m
10:16:12.449 [info] QUERY OK db=1.6ms decode=0.8ms
PRAGMA foreign_key_list(users) []
[0m[22m
10:16:12.450 [info] QUERY OK db=0.1ms
PRAGMA index_list(users) []
[0m[22m
10:16:12.451 [info] QUERY OK db=0.9ms
PRAGMA table_info(users) []
[0m[36m
10:16:12.458 [debug] Caching schema for Elixir.Drops.Relation.Repos.Sqlite.users with digest C7AAC1ECD5BD5C46EB1E457328B8FD970ADC3D67ADEFEB2B25D469BBF9844DDF to /workspace/drops-relation/tmp/cache/test/drops_relation_schema/sqlite/users.json
[0m[36m
10:16:12.467 [debug] Cached schema to /workspace/drops-relation/tmp/cache/test/drops_relation_schema/sqlite/users.json
[0m[36m
10:16:12.475 [debug] Schema cache hit for Elixir.Drops.Relation.Repos.Sqlite.users
[0m[22m
10:16:53.782 [info] Cleared entire schema cache
[0m[36m
10:16:53.937 [debug] Schema cache miss for Elixir.Drops.Relation.Repos.Sqlite.users (not cached: {:file_read, :enoent})
[0m[22m
10:16:53.945 [info] QUERY OK db=1.4ms decode=0.6ms
PRAGMA foreign_key_list(users) []
[0m[22m
10:16:53.946 [info] QUERY OK db=0.0ms
PRAGMA index_list(users) []
[0m[22m
10:16:53.946 [info] QUERY OK db=0.1ms
PRAGMA table_info(users) []
[0m[36m
10:16:53.954 [debug] Caching schema for Elixir.Drops.Relation.Repos.Sqlite.users with digest C7AAC1ECD5BD5C46EB1E457328B8FD970ADC3D67ADEFEB2B25D469BBF9844DDF to /workspace/drops-relation/tmp/cache/test/drops_relation_schema/sqlite/users.json
[0m[36m
10:16:53.962 [debug] Cached schema to /workspace/drops-relation/tmp/cache/test/drops_relation_schema/sqlite/users.json
[0m[36m
10:16:53.969 [debug] Schema cache hit for Elixir.Drops.Relation.Repos.Sqlite.users
[0m[22m
10:18:40.007 [info] Cleared entire schema cache
[0m[36m
10:18:40.165 [debug] Schema cache miss for Elixir.Drops.Relation.Repos.Sqlite.users (not cached: {:file_read, :enoent})
[0m[22m
10:18:40.173 [info] QUERY OK db=1.5ms decode=0.7ms
PRAGMA foreign_key_list(users) []
[0m[22m
10:18:40.174 [info] QUERY OK db=0.1ms
PRAGMA index_list(users) []
[0m[22m
10:18:40.174 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_age_index) []
[0m[22m
10:18:40.174 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_index) []
[0m[22m
10:18:40.174 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_email_index) []
[0m[22m
10:18:40.174 [info] QUERY OK db=0.0ms
PRAGMA table_info(users) []
[0m[36m
10:18:40.175 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
10:18:40.175 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
10:18:40.175 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
10:18:40.175 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
10:18:40.175 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
10:18:40.175 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
10:18:40.175 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
10:18:40.187 [debug] Caching schema for Elixir.Drops.Relation.Repos.Sqlite.users with digest C7AAC1ECD5BD5C46EB1E457328B8FD970ADC3D67ADEFEB2B25D469BBF9844DDF to /workspace/drops-relation/tmp/cache/test/drops_relation_schema/sqlite/users.json
[0m[36m
10:18:40.202 [debug] Cached schema to /workspace/drops-relation/tmp/cache/test/drops_relation_schema/sqlite/users.json
[0m[36m
10:18:40.209 [debug] Schema cache hit for Elixir.Drops.Relation.Repos.Sqlite.users
[0m[32m
10:18:40.318 [info] QUERY OK source="users" db=0.2ms
INSERT INTO "users" ("active","name","inserted_at","updated_at") VALUES (?1,?2,?3,?4) RETURNING "id" [false, "John", ~N[2025-07-16 10:18:40], ~N[2025-07-16 10:18:40]]
[0m[32m
10:18:40.318 [info] QUERY OK source="users" db=0.0ms
INSERT INTO "users" ("active","name","inserted_at","updated_at") VALUES (?1,?2,?3,?4) RETURNING "id" [true, "Jane", ~N[2025-07-16 10:18:40], ~N[2025-07-16 10:18:40]]
[0m[32m
10:18:40.318 [info] QUERY OK source="users" db=0.0ms
INSERT INTO "users" ("active","name","inserted_at","updated_at") VALUES (?1,?2,?3,?4) RETURNING "id" [false, "Joe", ~N[2025-07-16 10:18:40], ~N[2025-07-16 10:18:40]]
[0m[32m
10:18:40.319 [info] QUERY OK source="users" db=0.0ms
INSERT INTO "users" ("active","name","inserted_at","updated_at") VALUES (?1,?2,?3,?4) RETURNING "id" [true, "Jade", ~N[2025-07-16 10:18:40], ~N[2025-07-16 10:18:40]]
[0m[36m
10:18:40.322 [info] QUERY OK source="users" db=0.0ms
SELECT u0."id", u0."name", u0."active" FROM "users" AS u0 WHERE (u0."active" = ?) [true]
[0m[36m
10:18:40.326 [info] QUERY OK source="users" db=0.1ms
SELECT u0."id", u0."name", u0."active" FROM "users" AS u0 WHERE (u0."active" = ?) [true]
[0m