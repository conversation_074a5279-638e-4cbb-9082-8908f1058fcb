import Config

env = config_env()

if adapter = System.get_env("ADAPTER") do
  config :drops_relation, ecto_repos: [Module.concat(["Ecto", "Relation", "Repos", adapter])]
end

config :logger, backends: [{LoggerFileBackend, :drops_relation}]

config :logger, :drops_relation,
  path: Path.join("log", "{env}.log"),
  level: :debug,
  format: "$time $metadata[$level] $message\n"

config :drops_relation, Drops.Relation.Repos.Sqlite,
  adapter: Ecto.Adapters.SQLite3,
  database: "priv/repo/#{env}.sqlite",
  pool_size: 1,
  pool: Ecto.Adapters.SQL.Sandbox,
  queue_target: 5000,
  queue_interval: 1000,
  log: :info,
  priv: "priv/repo/sqlite"

config :drops_relation, Drops.Relation.Repos.Postgres,
  adapter: Ecto.Adapters.Postgres,
  username: "postgres",
  password: "postgres",
  hostname: "postgres",
  database: "drops_relation_#{env}",
  pool_size: 10,
  pool: Ecto.Adapters.SQL.Sandbox,
  queue_target: 5000,
  queue_interval: 1000,
  priv: "priv/repo/postgres",
  log: :info
