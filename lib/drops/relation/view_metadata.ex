defmodule Drops.Relation.ViewMetadata do
  @moduledoc """
  Extended metadata for relation views that includes view information.
  
  This struct extends the functionality of Ecto.Schema.Metadata to include
  information about the relation view that was used to load a struct.
  """
  
  @type t :: %__MODULE__{
          state: Ecto.Schema.Metadata.state(),
          source: Ecto.Schema.source(),
          context: Ecto.Schema.Metadata.context(),
          schema: module(),
          prefix: Ecto.Schema.prefix(),
          view_type: :view,
          view_name: String.t()
        }

  defstruct [
    :state,
    :source,
    :context,
    :schema,
    :prefix,
    :view_type,
    :view_name
  ]

  @doc """
  Creates a new ViewMetadata struct from an existing Ecto.Schema.Metadata
  and adds view information.
  """
  def from_ecto_metadata(%Ecto.Schema.Metadata{} = metadata, view_name) do
    %__MODULE__{
      state: metadata.state,
      source: metadata.source,
      context: metadata.context,
      schema: metadata.schema,
      prefix: metadata.prefix,
      view_type: :view,
      view_name: view_name
    }
  end

  @doc """
  Creates a new ViewMetadata struct with the given parameters.
  """
  def new(state, source, schema, view_name, prefix \\ nil, context \\ nil) do
    %__MODULE__{
      state: state,
      source: source,
      context: context,
      schema: schema,
      prefix: prefix,
      view_type: :view,
      view_name: view_name
    }
  end

  defimpl Inspect do
    import Inspect.Algebra

    def inspect(metadata, opts) do
      %{
        source: source,
        prefix: prefix,
        state: state,
        context: context,
        view_type: view_type,
        view_name: view_name
      } = metadata

      entries =
        for entry <- [state, prefix, source, view_type, view_name, context],
            entry != nil,
            do: to_doc(entry, opts)

      concat(["#Ecto.Schema.Metadata<"] ++ Enum.intersperse(entries, ", ") ++ [">"])
    end
  end
end
