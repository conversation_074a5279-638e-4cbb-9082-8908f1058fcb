10:07:01.489 [info] Cleared entire schema cache
10:07:01.630 [debug] Schema cache miss for Elixir.Drops.Relation.Repos.Sqlite.users (not cached: {:file_read, :enoent})
10:07:01.637 [info] QUERY OK db=1.5ms decode=0.8ms
PRAGMA foreign_key_list(users) []
10:07:01.637 [info] QUERY OK db=0.1ms
PRAGMA index_list(users) []
10:07:01.637 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_age_index) []
10:07:01.638 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_index) []
10:07:01.638 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_email_index) []
10:07:01.638 [info] QUERY OK db=0.0ms
PRAGMA table_info(users) []
10:07:01.639 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:07:01.639 [info] QUERY OK db=0.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:07:01.639 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:07:01.639 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:07:01.639 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:07:01.639 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:07:01.639 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:07:01.643 [debug] Caching schema for Elixir.Drops.Relation.Repos.Sqlite.users with digest C7AAC1ECD5BD5C46EB1E457328B8FD970ADC3D67ADEFEB2B25D469BBF9844DDF to /workspace/drops-relation/tmp/cache/test/drops_relation_schema/sqlite/users.json
10:07:01.655 [debug] Cached schema to /workspace/drops-relation/tmp/cache/test/drops_relation_schema/sqlite/users.json
10:07:01.662 [debug] Schema cache hit for Elixir.Drops.Relation.Repos.Sqlite.users
10:07:35.685 [info] Cleared entire schema cache
10:07:35.830 [debug] Schema cache miss for Elixir.Drops.Relation.Repos.Sqlite.users (not cached: {:file_read, :enoent})
10:07:35.838 [info] QUERY OK db=1.4ms decode=0.5ms
PRAGMA foreign_key_list(users) []
10:07:35.839 [info] QUERY OK db=0.2ms
PRAGMA index_list(users) []
10:07:35.839 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_age_index) []
10:07:35.839 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_index) []
10:07:35.839 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_email_index) []
10:07:35.839 [info] QUERY OK db=0.0ms
PRAGMA table_info(users) []
10:07:35.840 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:07:35.840 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:07:35.840 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:07:35.840 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:07:35.840 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:07:35.840 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:07:35.840 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:07:35.852 [debug] Caching schema for Elixir.Drops.Relation.Repos.Sqlite.users with digest C7AAC1ECD5BD5C46EB1E457328B8FD970ADC3D67ADEFEB2B25D469BBF9844DDF to /workspace/drops-relation/tmp/cache/test/drops_relation_schema/sqlite/users.json
10:07:35.868 [debug] Cached schema to /workspace/drops-relation/tmp/cache/test/drops_relation_schema/sqlite/users.json
10:07:35.875 [debug] Schema cache hit for Elixir.Drops.Relation.Repos.Sqlite.users
