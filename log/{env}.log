10:09:31.603 [info] Cleared entire schema cache
10:09:31.747 [debug] Schema cache miss for Elixir.Drops.Relation.Repos.Sqlite.users (not cached: {:file_read, :enoent})
10:09:31.753 [info] QUERY OK db=1.5ms decode=0.8ms
PRAGMA foreign_key_list(users) []
10:09:31.753 [info] QUERY OK db=0.1ms
PRAGMA index_list(users) []
10:09:31.753 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_age_index) []
10:09:31.753 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_index) []
10:09:31.753 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_email_index) []
10:09:31.754 [info] QUERY OK db=0.0ms
PRAGMA table_info(users) []
10:09:31.755 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:09:31.755 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:09:31.755 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:09:31.755 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:09:31.755 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:09:31.755 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:09:31.755 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
10:09:31.758 [debug] Caching schema for Elixir.Drops.Relation.Repos.Sqlite.users with digest C7AAC1ECD5BD5C46EB1E457328B8FD970ADC3D67ADEFEB2B25D469BBF9844DDF to /workspace/drops-relation/tmp/cache/test/drops_relation_schema/sqlite/users.json
10:09:31.777 [debug] Cached schema to /workspace/drops-relation/tmp/cache/test/drops_relation_schema/sqlite/users.json
10:09:31.784 [debug] Schema cache hit for Elixir.Drops.Relation.Repos.Sqlite.users
