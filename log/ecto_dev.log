12:41:00.228 [debug] Schema cache miss for Elixir.Drops.Relation.Repos.Sqlite.users (not cached: {:file_read, :enoent})
11:45:01.897 [info] QUERY ERROR queue=2.8ms
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_type = 'BASE TABLE'
AND table_name NOT LIKE 'schema_migrations'
 []
11:45:19.419 [info] QUERY OK db=1.4ms decode=1.0ms queue=2.0ms
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_type = 'BASE TABLE'
AND table_name NOT LIKE 'schema_migrations'
 []
11:45:19.429 [info] QUERY OK db=3.7ms queue=1.3ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["metadata_test"]
11:45:19.430 [info] QUERY OK db=0.7ms queue=0.3ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["metadata_test"]
11:45:19.432 [info] QUERY OK db=1.1ms queue=0.3ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["metadata_test"]
11:45:19.433 [info] QUERY OK db=0.4ms queue=0.2ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["metadata_test"]
11:45:19.443 [info] QUERY OK db=2.3ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["binary_id_organizations"]
11:45:19.444 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["binary_id_organizations"]
11:45:19.445 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["binary_id_organizations"]
11:45:19.445 [info] QUERY OK db=0.3ms queue=0.2ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["binary_id_organizations"]
11:45:19.449 [info] QUERY OK db=3.1ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["binary_id_users"]
11:45:19.450 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["binary_id_users"]
11:45:19.451 [info] QUERY OK db=0.9ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["binary_id_users"]
11:45:19.451 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["binary_id_users"]
11:45:19.455 [info] QUERY OK db=2.8ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["binary_id_posts"]
11:45:19.456 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["binary_id_posts"]
11:45:19.457 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["binary_id_posts"]
11:45:19.458 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["binary_id_posts"]
11:45:19.460 [info] QUERY OK db=1.8ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["uuid_organizations"]
11:45:19.460 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["uuid_organizations"]
11:45:19.462 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["uuid_organizations"]
11:45:19.462 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["uuid_organizations"]
11:45:19.465 [info] QUERY OK db=2.8ms queue=0.3ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["uuid_users"]
11:45:19.466 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["uuid_users"]
11:45:19.467 [info] QUERY OK db=0.9ms queue=0.3ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["uuid_users"]
11:45:19.468 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["uuid_users"]
11:45:19.471 [info] QUERY OK db=2.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["uuid_posts"]
11:45:19.471 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["uuid_posts"]
11:45:19.473 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["uuid_posts"]
11:45:19.473 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["uuid_posts"]
11:45:19.475 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["users"]
11:45:19.476 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["users"]
11:45:19.477 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["users"]
11:45:19.477 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["users"]
11:45:19.481 [info] QUERY OK db=3.1ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["user_groups"]
11:45:19.481 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["user_groups"]
11:45:19.482 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["user_groups"]
11:45:19.483 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["user_groups"]
11:45:19.485 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["groups"]
11:45:19.485 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["groups"]
11:45:19.487 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["groups"]
11:45:19.487 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["groups"]
11:45:19.489 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["association_parents"]
11:45:19.490 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["association_parents"]
11:45:19.491 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["association_parents"]
11:45:19.491 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["association_parents"]
11:45:19.494 [info] QUERY OK db=2.6ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["associations"]
11:45:19.495 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["associations"]
11:45:19.496 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["associations"]
11:45:19.496 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["associations"]
11:45:19.499 [info] QUERY OK db=2.6ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["association_items"]
11:45:19.500 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["association_items"]
11:45:19.501 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["association_items"]
11:45:19.501 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["association_items"]
11:45:19.503 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["basic_types"]
11:45:19.504 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["basic_types"]
11:45:19.505 [info] QUERY OK db=0.9ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["basic_types"]
11:45:19.506 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["basic_types"]
11:45:19.508 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["custom_pk"]
11:45:19.508 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["custom_pk"]
11:45:19.509 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["custom_pk"]
11:45:19.510 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["custom_pk"]
11:45:19.512 [info] QUERY OK db=1.6ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["no_pk"]
11:45:19.512 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["no_pk"]
11:45:19.513 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["no_pk"]
11:45:19.514 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["no_pk"]
11:45:19.516 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["composite_pk"]
11:45:19.517 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["composite_pk"]
11:45:19.518 [info] QUERY OK db=0.8ms queue=0.1ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["composite_pk"]
11:45:19.518 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["composite_pk"]
11:45:19.520 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["timestamps"]
11:45:19.521 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["timestamps"]
11:45:19.522 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["timestamps"]
11:45:19.522 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["timestamps"]
11:45:19.525 [info] QUERY OK db=2.5ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["posts"]
11:45:19.526 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["posts"]
11:45:19.527 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["posts"]
11:45:19.527 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["posts"]
11:45:19.529 [info] QUERY OK db=1.6ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["type_mapping_tests"]
11:45:19.530 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["type_mapping_tests"]
11:45:19.531 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["type_mapping_tests"]
11:45:19.531 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["type_mapping_tests"]
11:45:19.534 [info] QUERY OK db=1.9ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_types"]
11:45:19.534 [info] QUERY OK db=0.4ms queue=0.2ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_types"]
11:45:19.536 [info] QUERY OK db=1.0ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_types"]
11:45:19.536 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_types"]
11:45:19.539 [info] QUERY OK db=2.0ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_array_types"]
11:45:19.539 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_array_types"]
11:45:19.540 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_array_types"]
11:45:19.541 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_array_types"]
11:45:19.543 [info] QUERY OK db=1.7ms queue=0.3ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_geometric_types"]
11:45:19.544 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_geometric_types"]
11:45:19.545 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_geometric_types"]
11:45:19.545 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_geometric_types"]
11:45:19.549 [info] QUERY OK db=3.1ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["special_cases"]
11:45:19.549 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["special_cases"]
11:45:19.550 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["special_cases"]
11:45:19.551 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["special_cases"]
11:45:19.553 [info] QUERY OK db=1.6ms queue=0.3ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_serial_aliases"]
11:45:19.553 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_serial_aliases"]
11:45:19.555 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_serial_aliases"]
11:45:19.555 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_serial_aliases"]
11:45:19.560 [info] QUERY OK db=1.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'schema_migrations'
AND name NOT LIKE 'sqlite_%'
 []
11:45:19.561 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(users) []
11:45:19.561 [info] QUERY OK db=0.0ms
PRAGMA index_list(users) []
11:45:19.561 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_age_index) []
11:45:19.561 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_index) []
11:45:19.561 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_email_index) []
11:45:19.561 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["users"]
11:45:19.562 [info] QUERY OK db=0.0ms
PRAGMA table_info(users) []
11:45:19.562 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:45:19.562 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:45:19.562 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:45:19.563 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:45:19.563 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:45:19.563 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:45:19.564 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(groups) []
11:45:19.564 [info] QUERY OK db=0.0ms
PRAGMA index_list(groups) []
11:45:19.564 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["groups"]
11:45:19.565 [info] QUERY OK db=0.0ms
PRAGMA table_info(groups) []
11:45:19.565 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:45:19.565 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:45:19.565 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:45:19.565 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:45:19.565 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:45:19.565 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(user_groups) []
11:45:19.565 [info] QUERY OK db=0.0ms
PRAGMA index_list(user_groups) []
11:45:19.565 [info] QUERY OK db=0.0ms
PRAGMA index_info(user_groups_user_id_group_id_index) []
11:45:19.565 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["user_groups"]
11:45:19.566 [info] QUERY OK db=0.0ms
PRAGMA table_info(user_groups) []
11:45:19.566 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:45:19.566 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:45:19.566 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:45:19.566 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:45:19.566 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:45:19.566 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(association_parents) []
11:45:19.566 [info] QUERY OK db=0.0ms
PRAGMA index_list(association_parents) []
11:45:19.566 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["association_parents"]
11:45:19.567 [info] QUERY OK db=0.0ms
PRAGMA table_info(association_parents) []
11:45:19.567 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:45:19.567 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:45:19.567 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:45:19.567 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:45:19.567 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(associations) []
11:45:19.567 [info] QUERY OK db=0.0ms
PRAGMA index_list(associations) []
11:45:19.567 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["associations"]
11:45:19.567 [info] QUERY OK db=0.0ms
PRAGMA table_info(associations) []
11:45:19.567 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:45:19.568 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:45:19.568 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:45:19.568 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:45:19.568 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:45:19.568 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(association_items) []
11:45:19.570 [info] QUERY OK db=0.0ms
PRAGMA index_list(association_items) []
11:45:19.570 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["association_items"]
11:45:19.570 [info] QUERY OK db=0.0ms
PRAGMA table_info(association_items) []
11:45:19.570 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:45:19.570 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:45:19.570 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:45:19.570 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:45:19.571 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(basic_types) []
11:45:19.571 [info] QUERY OK db=0.0ms
PRAGMA index_list(basic_types) []
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
PRAGMA table_info(basic_types) []
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:45:19.571 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(custom_pk) []
11:45:19.572 [info] QUERY OK db=0.0ms
PRAGMA index_list(custom_pk) []
11:45:19.572 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_custom_pk_1) []
11:45:19.572 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["custom_pk"]
11:45:19.572 [info] QUERY OK db=0.0ms
PRAGMA table_info(custom_pk) []
11:45:19.572 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:45:19.572 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:45:19.572 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:45:19.572 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:45:19.572 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(no_pk) []
11:45:19.572 [info] QUERY OK db=0.0ms
PRAGMA index_list(no_pk) []
11:45:19.572 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["no_pk"]
11:45:19.573 [info] QUERY OK db=0.0ms
PRAGMA table_info(no_pk) []
11:45:19.573 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:45:19.573 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:45:19.573 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:45:19.573 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:45:19.577 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(composite_pk) []
11:45:19.577 [info] QUERY OK db=0.0ms
PRAGMA index_list(composite_pk) []
11:45:19.577 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_composite_pk_1) []
11:45:19.577 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["composite_pk"]
11:45:19.577 [info] QUERY OK db=0.0ms
PRAGMA table_info(composite_pk) []
11:45:19.577 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:45:19.577 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:45:19.577 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:45:19.577 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:45:19.577 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:45:19.578 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(timestamps) []
11:45:19.578 [info] QUERY OK db=0.0ms
PRAGMA index_list(timestamps) []
11:45:19.578 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["timestamps"]
11:45:19.578 [info] QUERY OK db=0.0ms
PRAGMA table_info(timestamps) []
11:45:19.578 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:45:19.578 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:45:19.578 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:45:19.578 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:45:19.579 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(posts) []
11:45:19.579 [info] QUERY OK db=0.0ms
PRAGMA index_list(posts) []
11:45:19.579 [info] QUERY OK db=0.0ms
PRAGMA index_info(posts_title_index) []
11:45:19.579 [info] QUERY OK db=0.0ms
PRAGMA index_info(posts_published_index) []
11:45:19.579 [info] QUERY OK db=0.0ms
PRAGMA index_info(posts_user_id_index) []
11:45:19.579 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["posts"]
11:45:19.579 [info] QUERY OK db=0.0ms
PRAGMA table_info(posts) []
11:45:19.580 [info] QUERY OK db=0.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:45:19.580 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(type_mapping_tests) []
11:45:19.584 [info] QUERY OK db=0.0ms
PRAGMA index_list(type_mapping_tests) []
11:45:19.584 [info] QUERY OK db=0.0ms
PRAGMA index_info(type_mapping_tests_boolean_type_date_type_index) []
11:45:19.584 [info] QUERY OK db=0.0ms
PRAGMA index_info(type_mapping_tests_text_type_index) []
11:45:19.584 [info] QUERY OK db=0.0ms
PRAGMA index_info(type_mapping_tests_integer_type_index) []
11:45:19.584 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["type_mapping_tests"]
11:45:19.584 [info] QUERY OK db=0.0ms
PRAGMA table_info(type_mapping_tests) []
11:45:19.584 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.584 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.584 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.585 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:45:19.586 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(postgres_types) []
11:45:19.586 [info] QUERY OK db=0.0ms
PRAGMA index_list(postgres_types) []
11:45:19.587 [info] QUERY OK db=0.0ms
PRAGMA index_info(postgres_types_varchar_type_text_type_index) []
11:45:19.587 [info] QUERY OK db=0.0ms
PRAGMA index_info(postgres_types_uuid_type_index) []
11:45:19.587 [info] QUERY OK db=0.0ms
PRAGMA index_info(postgres_types_integer_type_index) []
11:45:19.587 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["postgres_types"]
11:45:19.587 [info] QUERY OK db=0.0ms
PRAGMA table_info(postgres_types) []
11:45:19.587 [info] QUERY OK db=0.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.591 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.591 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.591 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.591 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.591 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.591 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.591 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.592 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.593 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.594 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.594 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.594 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.598 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.598 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.598 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:45:19.598 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:27.867 [info] QUERY OK db=0.8ms decode=1.0ms queue=1.7ms
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_type = 'BASE TABLE'
AND table_name NOT LIKE 'schema_migrations'
 []
11:47:27.874 [info] QUERY OK db=2.5ms queue=1.1ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["metadata_test"]
11:47:27.875 [info] QUERY OK db=0.6ms queue=0.3ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["metadata_test"]
11:47:27.877 [info] QUERY OK db=1.0ms queue=0.3ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["metadata_test"]
11:47:27.877 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["metadata_test"]
11:47:27.879 [info] QUERY OK db=1.8ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["binary_id_organizations"]
11:47:27.880 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["binary_id_organizations"]
11:47:27.881 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["binary_id_organizations"]
11:47:27.882 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["binary_id_organizations"]
11:47:27.885 [info] QUERY OK db=2.9ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["binary_id_users"]
11:47:27.889 [info] QUERY OK db=0.4ms queue=0.2ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["binary_id_users"]
11:47:27.890 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["binary_id_users"]
11:47:27.891 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["binary_id_users"]
11:47:27.894 [info] QUERY OK db=2.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["binary_id_posts"]
11:47:27.894 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["binary_id_posts"]
11:47:27.895 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["binary_id_posts"]
11:47:27.896 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["binary_id_posts"]
11:47:27.898 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["uuid_organizations"]
11:47:27.899 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["uuid_organizations"]
11:47:27.900 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["uuid_organizations"]
11:47:27.900 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["uuid_organizations"]
11:47:27.903 [info] QUERY OK db=2.8ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["uuid_users"]
11:47:27.904 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["uuid_users"]
11:47:27.905 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["uuid_users"]
11:47:27.906 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["uuid_users"]
11:47:27.909 [info] QUERY OK db=2.8ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["uuid_posts"]
11:47:27.909 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["uuid_posts"]
11:47:27.910 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["uuid_posts"]
11:47:27.911 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["uuid_posts"]
11:47:27.913 [info] QUERY OK db=1.8ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["users"]
11:47:27.913 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["users"]
11:47:27.915 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["users"]
11:47:27.915 [info] QUERY OK db=0.3ms queue=0.2ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["users"]
11:47:27.919 [info] QUERY OK db=3.0ms queue=0.3ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["user_groups"]
11:47:27.919 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["user_groups"]
11:47:27.920 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["user_groups"]
11:47:27.921 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["user_groups"]
11:47:27.923 [info] QUERY OK db=1.8ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["groups"]
11:47:27.924 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["groups"]
11:47:27.925 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["groups"]
11:47:27.925 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["groups"]
11:47:27.927 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["association_parents"]
11:47:27.928 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["association_parents"]
11:47:27.929 [info] QUERY OK db=0.8ms queue=0.1ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["association_parents"]
11:47:27.929 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["association_parents"]
11:47:27.932 [info] QUERY OK db=2.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["associations"]
11:47:27.933 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["associations"]
11:47:27.934 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["associations"]
11:47:27.935 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["associations"]
11:47:27.938 [info] QUERY OK db=2.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["association_items"]
11:47:27.938 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["association_items"]
11:47:27.939 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["association_items"]
11:47:27.940 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["association_items"]
11:47:27.942 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["basic_types"]
11:47:27.943 [info] QUERY OK db=0.4ms queue=0.2ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["basic_types"]
11:47:27.944 [info] QUERY OK db=0.7ms queue=0.1ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["basic_types"]
11:47:27.944 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["basic_types"]
11:47:27.946 [info] QUERY OK db=1.8ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["custom_pk"]
11:47:27.947 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["custom_pk"]
11:47:27.948 [info] QUERY OK db=0.7ms queue=0.1ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["custom_pk"]
11:47:27.948 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["custom_pk"]
11:47:27.950 [info] QUERY OK db=1.8ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["no_pk"]
11:47:27.951 [info] QUERY OK db=0.4ms queue=0.2ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["no_pk"]
11:47:27.952 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["no_pk"]
11:47:27.952 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["no_pk"]
11:47:27.954 [info] QUERY OK db=1.8ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["composite_pk"]
11:47:27.955 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["composite_pk"]
11:47:27.956 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["composite_pk"]
11:47:27.957 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["composite_pk"]
11:47:27.959 [info] QUERY OK db=1.9ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["timestamps"]
11:47:27.960 [info] QUERY OK db=0.4ms queue=0.2ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["timestamps"]
11:47:27.961 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["timestamps"]
11:47:27.961 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["timestamps"]
11:47:27.964 [info] QUERY OK db=2.6ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["posts"]
11:47:27.965 [info] QUERY OK db=0.4ms queue=0.2ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["posts"]
11:47:27.966 [info] QUERY OK db=0.9ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["posts"]
11:47:27.966 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["posts"]
11:47:27.968 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["type_mapping_tests"]
11:47:27.969 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["type_mapping_tests"]
11:47:27.970 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["type_mapping_tests"]
11:47:27.971 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["type_mapping_tests"]
11:47:27.973 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_types"]
11:47:27.973 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_types"]
11:47:27.975 [info] QUERY OK db=0.9ms queue=0.3ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_types"]
11:47:27.975 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_types"]
11:47:27.977 [info] QUERY OK db=1.6ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_array_types"]
11:47:27.978 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_array_types"]
11:47:27.979 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_array_types"]
11:47:27.979 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_array_types"]
11:47:27.981 [info] QUERY OK db=1.7ms queue=0.3ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_geometric_types"]
11:47:27.982 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_geometric_types"]
11:47:27.983 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_geometric_types"]
11:47:27.984 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_geometric_types"]
11:47:27.987 [info] QUERY OK db=3.4ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["special_cases"]
11:47:27.988 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["special_cases"]
11:47:27.989 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["special_cases"]
11:47:27.990 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["special_cases"]
11:47:27.992 [info] QUERY OK db=1.9ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_serial_aliases"]
11:47:27.992 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_serial_aliases"]
11:47:27.993 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_serial_aliases"]
11:47:27.994 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_serial_aliases"]
11:47:27.999 [info] QUERY OK db=1.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'schema_migrations'
AND name NOT LIKE 'sqlite_%'
 []
11:47:28.000 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(users) []
11:47:28.000 [info] QUERY OK db=0.0ms
PRAGMA index_list(users) []
11:47:28.000 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_age_index) []
11:47:28.000 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_index) []
11:47:28.000 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_email_index) []
11:47:28.000 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["users"]
11:47:28.001 [info] QUERY OK db=0.0ms
PRAGMA table_info(users) []
11:47:28.001 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:47:28.001 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:47:28.002 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:47:28.002 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:47:28.002 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:47:28.002 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:47:28.002 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(groups) []
11:47:28.002 [info] QUERY OK db=0.0ms
PRAGMA index_list(groups) []
11:47:28.002 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["groups"]
11:47:28.002 [info] QUERY OK db=0.0ms
PRAGMA table_info(groups) []
11:47:28.002 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:47:28.002 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:47:28.002 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:47:28.002 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:47:28.002 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:47:28.003 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(user_groups) []
11:47:28.003 [info] QUERY OK db=0.0ms
PRAGMA index_list(user_groups) []
11:47:28.003 [info] QUERY OK db=0.0ms
PRAGMA index_info(user_groups_user_id_group_id_index) []
11:47:28.003 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["user_groups"]
11:47:28.003 [info] QUERY OK db=0.0ms
PRAGMA table_info(user_groups) []
11:47:28.003 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:47:28.003 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:47:28.003 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:47:28.003 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:47:28.003 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:47:28.004 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(association_parents) []
11:47:28.004 [info] QUERY OK db=0.0ms
PRAGMA index_list(association_parents) []
11:47:28.004 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["association_parents"]
11:47:28.004 [info] QUERY OK db=0.0ms
PRAGMA table_info(association_parents) []
11:47:28.004 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:47:28.004 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:47:28.004 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:47:28.004 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:47:28.004 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(associations) []
11:47:28.004 [info] QUERY OK db=0.0ms
PRAGMA index_list(associations) []
11:47:28.007 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["associations"]
11:47:28.007 [info] QUERY OK db=0.0ms
PRAGMA table_info(associations) []
11:47:28.007 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:47:28.007 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:47:28.007 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:47:28.007 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:47:28.007 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:47:28.007 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(association_items) []
11:47:28.007 [info] QUERY OK db=0.0ms
PRAGMA index_list(association_items) []
11:47:28.008 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["association_items"]
11:47:28.008 [info] QUERY OK db=0.1ms
PRAGMA table_info(association_items) []
11:47:28.008 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:47:28.008 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:47:28.008 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:47:28.008 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:47:28.008 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:47:28.008 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(basic_types) []
11:47:28.009 [info] QUERY OK db=0.0ms
PRAGMA index_list(basic_types) []
11:47:28.009 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["basic_types"]
11:47:28.009 [info] QUERY OK db=0.0ms
PRAGMA table_info(basic_types) []
11:47:28.009 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:47:28.009 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:47:28.009 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:47:28.009 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:47:28.009 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:47:28.009 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:47:28.009 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:47:28.009 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:47:28.009 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:47:28.009 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(custom_pk) []
11:47:28.010 [info] QUERY OK db=0.0ms
PRAGMA index_list(custom_pk) []
11:47:28.010 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_custom_pk_1) []
11:47:28.010 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["custom_pk"]
11:47:28.013 [info] QUERY OK db=0.0ms
PRAGMA table_info(custom_pk) []
11:47:28.013 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:47:28.013 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:47:28.013 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:47:28.013 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:47:28.013 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(no_pk) []
11:47:28.013 [info] QUERY OK db=0.0ms
PRAGMA index_list(no_pk) []
11:47:28.014 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["no_pk"]
11:47:28.014 [info] QUERY OK db=0.0ms
PRAGMA table_info(no_pk) []
11:47:28.014 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:47:28.014 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:47:28.014 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:47:28.014 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:47:28.014 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(composite_pk) []
11:47:28.014 [info] QUERY OK db=0.0ms
PRAGMA index_list(composite_pk) []
11:47:28.014 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_composite_pk_1) []
11:47:28.014 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["composite_pk"]
11:47:28.015 [info] QUERY OK db=0.0ms
PRAGMA table_info(composite_pk) []
11:47:28.015 [info] QUERY OK db=0.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:47:28.015 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:47:28.015 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:47:28.016 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:47:28.016 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:47:28.016 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(timestamps) []
11:47:28.016 [info] QUERY OK db=0.0ms
PRAGMA index_list(timestamps) []
11:47:28.016 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["timestamps"]
11:47:28.016 [info] QUERY OK db=0.0ms
PRAGMA table_info(timestamps) []
11:47:28.016 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:47:28.016 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:47:28.016 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:47:28.016 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:47:28.016 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(posts) []
11:47:28.017 [info] QUERY OK db=0.1ms
PRAGMA index_list(posts) []
11:47:28.017 [info] QUERY OK db=0.0ms
PRAGMA index_info(posts_title_index) []
11:47:28.020 [info] QUERY OK db=0.0ms
PRAGMA index_info(posts_published_index) []
11:47:28.021 [info] QUERY OK db=0.0ms
PRAGMA index_info(posts_user_id_index) []
11:47:28.021 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["posts"]
11:47:28.021 [info] QUERY OK db=0.0ms
PRAGMA table_info(posts) []
11:47:28.021 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:47:28.021 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:47:28.021 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:47:28.021 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:47:28.021 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:47:28.021 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:47:28.021 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:47:28.022 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:47:28.022 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(type_mapping_tests) []
11:47:28.022 [info] QUERY OK db=0.0ms
PRAGMA index_list(type_mapping_tests) []
11:47:28.022 [info] QUERY OK db=0.0ms
PRAGMA index_info(type_mapping_tests_boolean_type_date_type_index) []
11:47:28.022 [info] QUERY OK db=0.0ms
PRAGMA index_info(type_mapping_tests_text_type_index) []
11:47:28.022 [info] QUERY OK db=0.0ms
PRAGMA index_info(type_mapping_tests_integer_type_index) []
11:47:28.022 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["type_mapping_tests"]
11:47:28.022 [info] QUERY OK db=0.0ms
PRAGMA table_info(type_mapping_tests) []
11:47:28.023 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.023 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.023 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.023 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.023 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.023 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.023 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.023 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.023 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.023 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.023 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.023 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.023 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.023 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.027 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.027 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.027 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.027 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.027 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.027 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:47:28.027 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(postgres_types) []
11:47:28.027 [info] QUERY OK db=0.0ms
PRAGMA index_list(postgres_types) []
11:47:28.027 [info] QUERY OK db=0.0ms
PRAGMA index_info(postgres_types_varchar_type_text_type_index) []
11:47:28.028 [info] QUERY OK db=0.0ms
PRAGMA index_info(postgres_types_uuid_type_index) []
11:47:28.028 [info] QUERY OK db=0.1ms
PRAGMA index_info(postgres_types_integer_type_index) []
11:47:28.028 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["postgres_types"]
11:47:28.028 [info] QUERY OK db=0.1ms
PRAGMA table_info(postgres_types) []
11:47:28.028 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.028 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.028 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.029 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.029 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.029 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.029 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.029 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.029 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.029 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.029 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.029 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.030 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.030 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.030 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.030 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.030 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.030 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.030 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.030 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.030 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.033 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.033 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.033 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.033 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.033 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.033 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.034 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.034 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.034 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.034 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.034 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.034 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.034 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.034 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.034 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.034 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.034 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.035 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:47:28.035 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(postgres_array_types) []
11:47:28.035 [info] QUERY OK db=0.0ms
PRAGMA index_list(postgres_array_types) []
11:47:28.035 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["postgres_array_types"]
11:47:28.035 [info] QUERY OK db=0.0ms
PRAGMA table_info(postgres_array_types) []
11:47:28.035 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_array_types"]
11:47:28.035 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_array_types"]
11:47:28.035 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_array_types"]
11:47:28.035 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_array_types"]
11:47:28.036 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_array_types"]
11:47:28.036 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_array_types"]
11:47:28.036 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_array_types"]
11:47:28.036 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(postgres_geometric_types) []
11:47:28.036 [info] QUERY OK db=0.0ms
PRAGMA index_list(postgres_geometric_types) []
11:47:28.036 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["postgres_geometric_types"]
11:47:28.040 [info] QUERY OK db=0.0ms
PRAGMA table_info(postgres_geometric_types) []
11:47:28.040 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:47:28.040 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:47:28.040 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:47:28.040 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:47:28.040 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:47:28.040 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:47:28.041 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:47:28.041 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:47:28.041 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:47:28.041 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:47:28.041 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(special_cases) []
11:47:28.041 [info] QUERY OK db=0.0ms
PRAGMA index_list(special_cases) []
11:47:28.041 [info] QUERY OK db=0.0ms
PRAGMA index_info(special_cases_required_field_index) []
11:47:28.041 [info] QUERY OK db=0.0ms
PRAGMA index_info(special_cases_user_id_index) []
11:47:28.041 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["special_cases"]
11:47:28.041 [info] QUERY OK db=0.0ms
PRAGMA table_info(special_cases) []
11:47:28.041 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:47:28.041 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:47:28.041 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:47:28.041 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:47:28.042 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:47:28.042 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:47:28.042 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:47:28.042 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:47:28.042 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:47:28.042 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:47:28.042 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(postgres_serial_aliases) []
11:47:28.042 [info] QUERY OK db=0.0ms
PRAGMA index_list(postgres_serial_aliases) []
11:47:28.046 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["postgres_serial_aliases"]
11:47:28.046 [info] QUERY OK db=0.0ms
PRAGMA table_info(postgres_serial_aliases) []
11:47:28.046 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_serial_aliases"]
11:47:28.046 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_serial_aliases"]
11:47:28.046 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_serial_aliases"]
11:47:28.046 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_serial_aliases"]
11:47:28.046 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_serial_aliases"]
11:47:28.046 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_serial_aliases"]
11:47:28.047 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_serial_aliases"]
11:47:28.047 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(metadata_test_new) []
11:47:28.047 [info] QUERY OK db=0.0ms
PRAGMA index_list(metadata_test_new) []
11:47:28.047 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["metadata_test_new"]
11:47:28.047 [info] QUERY OK db=0.0ms
PRAGMA table_info(metadata_test_new) []
11:47:28.047 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:47:28.047 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:47:28.048 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:47:28.048 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:47:28.048 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:47:28.048 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:47:28.048 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:47:28.048 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:47:28.048 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:47:28.048 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(metadata_test) []
11:47:28.048 [info] QUERY OK db=0.0ms
PRAGMA index_list(metadata_test) []
11:47:28.048 [info] QUERY OK db=0.0ms
PRAGMA index_info(metadata_test_name_priority_index) []
11:47:28.048 [info] QUERY OK db=0.0ms
PRAGMA index_info(metadata_test_status_index) []
11:47:28.048 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["metadata_test"]
11:47:28.049 [info] QUERY OK db=0.0ms
PRAGMA table_info(metadata_test) []
11:47:28.049 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:47:28.049 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:47:28.049 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:47:28.049 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:47:28.049 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:47:28.049 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:47:28.049 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:47:28.049 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:47:28.049 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:47:28.053 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(binary_id_organizations) []
11:47:28.053 [info] QUERY OK db=0.0ms
PRAGMA index_list(binary_id_organizations) []
11:47:28.053 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_binary_id_organizations_1) []
11:47:28.053 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["binary_id_organizations"]
11:47:28.054 [info] QUERY OK db=0.0ms
PRAGMA table_info(binary_id_organizations) []
11:47:28.054 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_organizations"]
11:47:28.054 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_organizations"]
11:47:28.054 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_organizations"]
11:47:28.054 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_organizations"]
11:47:28.054 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(binary_id_users) []
11:47:28.054 [info] QUERY OK db=0.0ms
PRAGMA index_list(binary_id_users) []
11:47:28.054 [info] QUERY OK db=0.0ms
PRAGMA index_info(binary_id_users_organization_id_index) []
11:47:28.054 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_binary_id_users_1) []
11:47:28.054 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["binary_id_users"]
11:47:28.054 [info] QUERY OK db=0.0ms
PRAGMA table_info(binary_id_users) []
11:47:28.054 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_users"]
11:47:28.054 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_users"]
11:47:28.055 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_users"]
11:47:28.055 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_users"]
11:47:28.055 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_users"]
11:47:28.055 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_users"]
11:47:28.055 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(binary_id_posts) []
11:47:28.055 [info] QUERY OK db=0.0ms
PRAGMA index_list(binary_id_posts) []
11:47:28.055 [info] QUERY OK db=0.0ms
PRAGMA index_info(binary_id_posts_user_id_index) []
11:47:28.055 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_binary_id_posts_1) []
11:47:28.055 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["binary_id_posts"]
11:47:28.055 [info] QUERY OK db=0.0ms
PRAGMA table_info(binary_id_posts) []
11:47:28.056 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_posts"]
11:47:28.056 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_posts"]
11:47:28.056 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_posts"]
11:47:28.056 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_posts"]
11:47:28.056 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_posts"]
11:47:28.056 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_posts"]
11:47:28.056 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(uuid_organizations) []
11:47:28.060 [info] QUERY OK db=0.0ms
PRAGMA index_list(uuid_organizations) []
11:47:28.060 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_uuid_organizations_1) []
11:47:28.060 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["uuid_organizations"]
11:47:28.060 [info] QUERY OK db=0.0ms
PRAGMA table_info(uuid_organizations) []
11:47:28.060 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_organizations"]
11:47:28.060 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_organizations"]
11:47:28.060 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_organizations"]
11:47:28.060 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_organizations"]
11:47:28.060 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(uuid_users) []
11:47:28.060 [info] QUERY OK db=0.0ms
PRAGMA index_list(uuid_users) []
11:47:28.060 [info] QUERY OK db=0.0ms
PRAGMA index_info(uuid_users_organization_id_index) []
11:47:28.060 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_uuid_users_1) []
11:47:28.060 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["uuid_users"]
11:47:28.060 [info] QUERY OK db=0.0ms
PRAGMA table_info(uuid_users) []
11:47:28.061 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_users"]
11:47:28.061 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_users"]
11:47:28.061 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_users"]
11:47:28.061 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_users"]
11:47:28.061 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_users"]
11:47:28.061 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_users"]
11:47:28.061 [info] QUERY OK db=0.1ms
PRAGMA foreign_key_list(uuid_posts) []
11:47:28.061 [info] QUERY OK db=0.0ms
PRAGMA index_list(uuid_posts) []
11:47:28.061 [info] QUERY OK db=0.1ms
PRAGMA index_info(uuid_posts_user_id_index) []
11:47:28.062 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_uuid_posts_1) []
11:47:28.062 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["uuid_posts"]
11:47:28.062 [info] QUERY OK db=0.0ms
PRAGMA table_info(uuid_posts) []
11:47:28.062 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_posts"]
11:47:28.062 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_posts"]
11:47:28.062 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_posts"]
11:47:28.067 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_posts"]
11:47:28.067 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_posts"]
11:47:28.067 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_posts"]
11:47:28.067 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(proper_date_types) []
11:47:28.067 [info] QUERY OK db=0.0ms
PRAGMA index_list(proper_date_types) []
11:47:28.067 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["proper_date_types"]
11:47:28.067 [info] QUERY OK db=0.0ms
PRAGMA table_info(proper_date_types) []
11:47:28.067 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:47:28.068 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:47:28.068 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:47:28.068 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:47:28.068 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:47:28.068 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:47:28.068 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:47:28.069 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:47:28.069 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:48:14.521 [info] QUERY OK db=1.2ms decode=1.6ms queue=2.3ms
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_type = 'BASE TABLE'
AND table_name NOT LIKE 'schema_migrations'
 []
11:48:14.529 [info] QUERY OK db=3.7ms queue=1.3ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["metadata_test"]
11:48:14.530 [info] QUERY OK db=0.6ms queue=0.3ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["metadata_test"]
11:48:14.532 [info] QUERY OK db=1.0ms queue=0.3ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["metadata_test"]
11:48:14.532 [info] QUERY OK db=0.3ms queue=0.2ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["metadata_test"]
11:48:14.535 [info] QUERY OK db=2.1ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["binary_id_organizations"]
11:48:14.536 [info] QUERY OK db=0.4ms queue=0.2ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["binary_id_organizations"]
11:48:14.537 [info] QUERY OK db=0.7ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["binary_id_organizations"]
11:48:14.537 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["binary_id_organizations"]
11:48:14.541 [info] QUERY OK db=3.3ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["binary_id_users"]
11:48:14.549 [info] QUERY OK db=1.8ms queue=1.7ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["binary_id_users"]
11:48:14.552 [info] QUERY OK db=2.2ms queue=0.7ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["binary_id_users"]
11:48:14.554 [info] QUERY OK db=1.0ms queue=0.5ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["binary_id_users"]
11:48:14.561 [info] QUERY OK db=6.7ms queue=0.6ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["binary_id_posts"]
11:48:14.564 [info] QUERY OK db=2.2ms queue=0.6ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["binary_id_posts"]
11:48:14.567 [info] QUERY OK db=1.9ms queue=0.9ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["binary_id_posts"]
11:48:14.569 [info] QUERY OK db=0.7ms queue=0.6ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["binary_id_posts"]
11:48:14.572 [info] QUERY OK db=2.2ms queue=0.7ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["uuid_organizations"]
11:48:14.573 [info] QUERY OK db=0.5ms queue=0.3ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["uuid_organizations"]
11:48:14.574 [info] QUERY OK db=0.9ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["uuid_organizations"]
11:48:14.575 [info] QUERY OK db=0.5ms queue=0.2ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["uuid_organizations"]
11:48:14.580 [info] QUERY OK db=4.1ms queue=0.4ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["uuid_users"]
11:48:14.581 [info] QUERY OK db=0.6ms queue=0.4ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["uuid_users"]
11:48:14.582 [info] QUERY OK db=0.9ms queue=0.3ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["uuid_users"]
11:48:14.583 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["uuid_users"]
11:48:14.586 [info] QUERY OK db=2.9ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["uuid_posts"]
11:48:14.587 [info] QUERY OK db=0.5ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["uuid_posts"]
11:48:14.588 [info] QUERY OK db=0.8ms queue=0.3ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["uuid_posts"]
11:48:14.588 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["uuid_posts"]
11:48:14.591 [info] QUERY OK db=1.9ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["users"]
11:48:14.591 [info] QUERY OK db=0.5ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["users"]
11:48:14.593 [info] QUERY OK db=0.9ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["users"]
11:48:14.593 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["users"]
11:48:14.597 [info] QUERY OK db=3.1ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["user_groups"]
11:48:14.597 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["user_groups"]
11:48:14.598 [info] QUERY OK db=0.9ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["user_groups"]
11:48:14.599 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["user_groups"]
11:48:14.601 [info] QUERY OK db=1.9ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["groups"]
11:48:14.602 [info] QUERY OK db=0.5ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["groups"]
11:48:14.603 [info] QUERY OK db=0.8ms queue=0.3ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["groups"]
11:48:14.604 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["groups"]
11:48:14.606 [info] QUERY OK db=1.9ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["association_parents"]
11:48:14.606 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["association_parents"]
11:48:14.608 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["association_parents"]
11:48:14.608 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["association_parents"]
11:48:14.612 [info] QUERY OK db=2.9ms queue=0.3ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["associations"]
11:48:14.612 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["associations"]
11:48:14.613 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["associations"]
11:48:14.614 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["associations"]
11:48:14.617 [info] QUERY OK db=2.8ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["association_items"]
11:48:14.618 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["association_items"]
11:48:14.619 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["association_items"]
11:48:14.619 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["association_items"]
11:48:14.621 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["basic_types"]
11:48:14.622 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["basic_types"]
11:48:14.623 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["basic_types"]
11:48:14.624 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["basic_types"]
11:48:14.626 [info] QUERY OK db=1.9ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["custom_pk"]
11:48:14.627 [info] QUERY OK db=0.4ms queue=0.2ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["custom_pk"]
11:48:14.628 [info] QUERY OK db=0.9ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["custom_pk"]
11:48:14.628 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["custom_pk"]
11:48:14.631 [info] QUERY OK db=2.1ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["no_pk"]
11:48:14.631 [info] QUERY OK db=0.4ms queue=0.2ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["no_pk"]
11:48:14.633 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["no_pk"]
11:48:14.633 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["no_pk"]
11:48:14.635 [info] QUERY OK db=1.9ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["composite_pk"]
11:48:14.636 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["composite_pk"]
11:48:14.637 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["composite_pk"]
11:48:14.637 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["composite_pk"]
11:48:14.640 [info] QUERY OK db=1.8ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["timestamps"]
11:48:14.640 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["timestamps"]
11:48:14.641 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["timestamps"]
11:48:14.642 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["timestamps"]
11:48:14.645 [info] QUERY OK db=2.9ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["posts"]
11:48:14.646 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["posts"]
11:48:14.647 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["posts"]
11:48:14.647 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["posts"]
11:48:14.649 [info] QUERY OK db=1.9ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["type_mapping_tests"]
11:48:14.650 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["type_mapping_tests"]
11:48:14.651 [info] QUERY OK db=0.9ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["type_mapping_tests"]
11:48:14.652 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["type_mapping_tests"]
11:48:14.654 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_types"]
11:48:14.654 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_types"]
11:48:14.656 [info] QUERY OK db=0.9ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_types"]
11:48:14.656 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_types"]
11:48:14.658 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_array_types"]
11:48:14.659 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_array_types"]
11:48:14.660 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_array_types"]
11:48:14.661 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_array_types"]
11:48:14.663 [info] QUERY OK db=1.7ms queue=0.3ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_geometric_types"]
11:48:14.663 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_geometric_types"]
11:48:14.664 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_geometric_types"]
11:48:14.665 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_geometric_types"]
11:48:14.668 [info] QUERY OK db=3.1ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["special_cases"]
11:48:14.669 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["special_cases"]
11:48:14.670 [info] QUERY OK db=0.9ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["special_cases"]
11:48:14.671 [info] QUERY OK db=0.2ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["special_cases"]
11:48:14.673 [info] QUERY OK db=1.7ms queue=0.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["postgres_serial_aliases"]
11:48:14.673 [info] QUERY OK db=0.4ms queue=0.1ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["postgres_serial_aliases"]
11:48:14.675 [info] QUERY OK db=0.8ms queue=0.2ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["postgres_serial_aliases"]
11:48:14.675 [info] QUERY OK db=0.3ms queue=0.1ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["postgres_serial_aliases"]
11:48:14.679 [info] QUERY OK db=0.9ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'schema_migrations'
AND name NOT LIKE 'sqlite_%'
 []
11:48:14.680 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(users) []
11:48:14.681 [info] QUERY OK db=0.0ms
PRAGMA index_list(users) []
11:48:14.681 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_age_index) []
11:48:14.681 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_name_index) []
11:48:14.681 [info] QUERY OK db=0.0ms
PRAGMA index_info(users_email_index) []
11:48:14.681 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["users"]
11:48:14.682 [info] QUERY OK db=0.0ms
PRAGMA table_info(users) []
11:48:14.682 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:48:14.682 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:48:14.682 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:48:14.682 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:48:14.682 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:48:14.682 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
11:48:14.682 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(groups) []
11:48:14.682 [info] QUERY OK db=0.0ms
PRAGMA index_list(groups) []
11:48:14.682 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["groups"]
11:48:14.683 [info] QUERY OK db=0.0ms
PRAGMA table_info(groups) []
11:48:14.683 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:48:14.683 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:48:14.683 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:48:14.683 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:48:14.683 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["groups"]
11:48:14.683 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(user_groups) []
11:48:14.683 [info] QUERY OK db=0.0ms
PRAGMA index_list(user_groups) []
11:48:14.683 [info] QUERY OK db=0.0ms
PRAGMA index_info(user_groups_user_id_group_id_index) []
11:48:14.684 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["user_groups"]
11:48:14.684 [info] QUERY OK db=0.1ms
PRAGMA table_info(user_groups) []
11:48:14.684 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:48:14.684 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:48:14.684 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:48:14.684 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:48:14.684 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["user_groups"]
11:48:14.684 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(association_parents) []
11:48:14.684 [info] QUERY OK db=0.0ms
PRAGMA index_list(association_parents) []
11:48:14.685 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["association_parents"]
11:48:14.685 [info] QUERY OK db=0.0ms
PRAGMA table_info(association_parents) []
11:48:14.685 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:48:14.685 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:48:14.685 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:48:14.685 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_parents"]
11:48:14.685 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(associations) []
11:48:14.685 [info] QUERY OK db=0.0ms
PRAGMA index_list(associations) []
11:48:14.688 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["associations"]
11:48:14.688 [info] QUERY OK db=0.0ms
PRAGMA table_info(associations) []
11:48:14.688 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:48:14.688 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:48:14.688 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:48:14.688 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:48:14.688 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["associations"]
11:48:14.688 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(association_items) []
11:48:14.688 [info] QUERY OK db=0.0ms
PRAGMA index_list(association_items) []
11:48:14.689 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["association_items"]
11:48:14.689 [info] QUERY OK db=0.0ms
PRAGMA table_info(association_items) []
11:48:14.689 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:48:14.689 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:48:14.689 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:48:14.689 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:48:14.689 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["association_items"]
11:48:14.689 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(basic_types) []
11:48:14.689 [info] QUERY OK db=0.0ms
PRAGMA index_list(basic_types) []
11:48:14.689 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["basic_types"]
11:48:14.690 [info] QUERY OK db=0.1ms
PRAGMA table_info(basic_types) []
11:48:14.690 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:48:14.690 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:48:14.690 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:48:14.690 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:48:14.690 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:48:14.690 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:48:14.690 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:48:14.690 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:48:14.690 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["basic_types"]
11:48:14.690 [info] QUERY OK db=0.2ms
PRAGMA foreign_key_list(custom_pk) []
11:48:14.691 [info] QUERY OK db=0.0ms
PRAGMA index_list(custom_pk) []
11:48:14.691 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_custom_pk_1) []
11:48:14.695 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["custom_pk"]
11:48:14.695 [info] QUERY OK db=0.0ms
PRAGMA table_info(custom_pk) []
11:48:14.695 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:48:14.695 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:48:14.695 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:48:14.695 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["custom_pk"]
11:48:14.695 [info] QUERY OK db=0.2ms
PRAGMA foreign_key_list(no_pk) []
11:48:14.696 [info] QUERY OK db=0.1ms
PRAGMA index_list(no_pk) []
11:48:14.696 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["no_pk"]
11:48:14.696 [info] QUERY OK db=0.0ms
PRAGMA table_info(no_pk) []
11:48:14.696 [info] QUERY OK db=0.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:48:14.696 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:48:14.696 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:48:14.697 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["no_pk"]
11:48:14.697 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(composite_pk) []
11:48:14.697 [info] QUERY OK db=0.0ms
PRAGMA index_list(composite_pk) []
11:48:14.697 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_composite_pk_1) []
11:48:14.697 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["composite_pk"]
11:48:14.697 [info] QUERY OK db=0.1ms
PRAGMA table_info(composite_pk) []
11:48:14.697 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:48:14.697 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:48:14.697 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:48:14.697 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:48:14.697 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["composite_pk"]
11:48:14.697 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(timestamps) []
11:48:14.698 [info] QUERY OK db=0.0ms
PRAGMA index_list(timestamps) []
11:48:14.698 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["timestamps"]
11:48:14.698 [info] QUERY OK db=0.0ms
PRAGMA table_info(timestamps) []
11:48:14.698 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:48:14.698 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:48:14.698 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:48:14.698 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["timestamps"]
11:48:14.698 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(posts) []
11:48:14.698 [info] QUERY OK db=0.0ms
PRAGMA index_list(posts) []
11:48:14.698 [info] QUERY OK db=0.0ms
PRAGMA index_info(posts_title_index) []
11:48:14.698 [info] QUERY OK db=0.0ms
PRAGMA index_info(posts_published_index) []
11:48:14.698 [info] QUERY OK db=0.0ms
PRAGMA index_info(posts_user_id_index) []
11:48:14.701 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["posts"]
11:48:14.701 [info] QUERY OK db=0.0ms
PRAGMA table_info(posts) []
11:48:14.702 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:48:14.702 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:48:14.702 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:48:14.702 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:48:14.702 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:48:14.702 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:48:14.702 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:48:14.702 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
11:48:14.702 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(type_mapping_tests) []
11:48:14.702 [info] QUERY OK db=0.0ms
PRAGMA index_list(type_mapping_tests) []
11:48:14.702 [info] QUERY OK db=0.0ms
PRAGMA index_info(type_mapping_tests_boolean_type_date_type_index) []
11:48:14.702 [info] QUERY OK db=0.0ms
PRAGMA index_info(type_mapping_tests_text_type_index) []
11:48:14.702 [info] QUERY OK db=0.0ms
PRAGMA index_info(type_mapping_tests_integer_type_index) []
11:48:14.702 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["type_mapping_tests"]
11:48:14.703 [info] QUERY OK db=0.1ms
PRAGMA table_info(type_mapping_tests) []
11:48:14.703 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.703 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.703 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.703 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.703 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.703 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.703 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.703 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.703 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.703 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.704 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.704 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.704 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.706 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.706 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.706 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.706 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.706 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.706 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.707 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["type_mapping_tests"]
11:48:14.707 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(postgres_types) []
11:48:14.707 [info] QUERY OK db=0.0ms
PRAGMA index_list(postgres_types) []
11:48:14.707 [info] QUERY OK db=0.0ms
PRAGMA index_info(postgres_types_varchar_type_text_type_index) []
11:48:14.707 [info] QUERY OK db=0.0ms
PRAGMA index_info(postgres_types_uuid_type_index) []
11:48:14.707 [info] QUERY OK db=0.0ms
PRAGMA index_info(postgres_types_integer_type_index) []
11:48:14.707 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["postgres_types"]
11:48:14.707 [info] QUERY OK db=0.0ms
PRAGMA table_info(postgres_types) []
11:48:14.707 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.707 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.707 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.707 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.707 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.707 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.707 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.707 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.708 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.708 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.708 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.708 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.708 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.708 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.708 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.713 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.713 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.713 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.713 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.713 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.713 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.713 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.713 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.713 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.714 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_types"]
11:48:14.715 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(postgres_array_types) []
11:48:14.715 [info] QUERY OK db=0.0ms
PRAGMA index_list(postgres_array_types) []
11:48:14.715 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["postgres_array_types"]
11:48:14.715 [info] QUERY OK db=0.0ms
PRAGMA table_info(postgres_array_types) []
11:48:14.715 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_array_types"]
11:48:14.715 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_array_types"]
11:48:14.715 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_array_types"]
11:48:14.715 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_array_types"]
11:48:14.715 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_array_types"]
11:48:14.719 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_array_types"]
11:48:14.720 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_array_types"]
11:48:14.720 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(postgres_geometric_types) []
11:48:14.720 [info] QUERY OK db=0.0ms
PRAGMA index_list(postgres_geometric_types) []
11:48:14.720 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["postgres_geometric_types"]
11:48:14.720 [info] QUERY OK db=0.0ms
PRAGMA table_info(postgres_geometric_types) []
11:48:14.720 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:48:14.720 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:48:14.720 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:48:14.720 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:48:14.720 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:48:14.720 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:48:14.720 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:48:14.720 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:48:14.720 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:48:14.720 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_geometric_types"]
11:48:14.720 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(special_cases) []
11:48:14.720 [info] QUERY OK db=0.0ms
PRAGMA index_list(special_cases) []
11:48:14.720 [info] QUERY OK db=0.0ms
PRAGMA index_info(special_cases_required_field_index) []
11:48:14.721 [info] QUERY OK db=0.0ms
PRAGMA index_info(special_cases_user_id_index) []
11:48:14.721 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["special_cases"]
11:48:14.721 [info] QUERY OK db=0.0ms
PRAGMA table_info(special_cases) []
11:48:14.721 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:48:14.721 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:48:14.721 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:48:14.721 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:48:14.721 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:48:14.721 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:48:14.721 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:48:14.721 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:48:14.725 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:48:14.725 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["special_cases"]
11:48:14.725 [info] QUERY OK db=0.1ms
PRAGMA foreign_key_list(postgres_serial_aliases) []
11:48:14.725 [info] QUERY OK db=0.1ms
PRAGMA index_list(postgres_serial_aliases) []
11:48:14.725 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["postgres_serial_aliases"]
11:48:14.726 [info] QUERY OK db=0.0ms
PRAGMA table_info(postgres_serial_aliases) []
11:48:14.726 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_serial_aliases"]
11:48:14.726 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_serial_aliases"]
11:48:14.726 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_serial_aliases"]
11:48:14.726 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_serial_aliases"]
11:48:14.726 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_serial_aliases"]
11:48:14.726 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_serial_aliases"]
11:48:14.726 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["postgres_serial_aliases"]
11:48:14.726 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(metadata_test_new) []
11:48:14.727 [info] QUERY OK db=0.0ms
PRAGMA index_list(metadata_test_new) []
11:48:14.727 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["metadata_test_new"]
11:48:14.727 [info] QUERY OK db=0.0ms
PRAGMA table_info(metadata_test_new) []
11:48:14.727 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:48:14.727 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:48:14.727 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:48:14.727 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:48:14.728 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:48:14.728 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:48:14.728 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:48:14.728 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:48:14.728 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test_new"]
11:48:14.728 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(metadata_test) []
11:48:14.728 [info] QUERY OK db=0.0ms
PRAGMA index_list(metadata_test) []
11:48:14.728 [info] QUERY OK db=0.0ms
PRAGMA index_info(metadata_test_name_priority_index) []
11:48:14.728 [info] QUERY OK db=0.0ms
PRAGMA index_info(metadata_test_status_index) []
11:48:14.729 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["metadata_test"]
11:48:14.729 [info] QUERY OK db=0.0ms
PRAGMA table_info(metadata_test) []
11:48:14.729 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:48:14.729 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:48:14.729 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:48:14.729 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:48:14.729 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:48:14.729 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:48:14.729 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:48:14.729 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:48:14.729 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["metadata_test"]
11:48:14.733 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(binary_id_organizations) []
11:48:14.733 [info] QUERY OK db=0.0ms
PRAGMA index_list(binary_id_organizations) []
11:48:14.733 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_binary_id_organizations_1) []
11:48:14.733 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["binary_id_organizations"]
11:48:14.734 [info] QUERY OK db=0.1ms
PRAGMA table_info(binary_id_organizations) []
11:48:14.734 [info] QUERY OK db=0.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_organizations"]
11:48:14.734 [info] QUERY OK db=0.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_organizations"]
11:48:14.734 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_organizations"]
11:48:14.735 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_organizations"]
11:48:14.735 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(binary_id_users) []
11:48:14.735 [info] QUERY OK db=0.0ms
PRAGMA index_list(binary_id_users) []
11:48:14.735 [info] QUERY OK db=0.0ms
PRAGMA index_info(binary_id_users_organization_id_index) []
11:48:14.735 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_binary_id_users_1) []
11:48:14.735 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["binary_id_users"]
11:48:14.735 [info] QUERY OK db=0.0ms
PRAGMA table_info(binary_id_users) []
11:48:14.735 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_users"]
11:48:14.735 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_users"]
11:48:14.735 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_users"]
11:48:14.735 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_users"]
11:48:14.736 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_users"]
11:48:14.736 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_users"]
11:48:14.736 [info] QUERY OK db=0.1ms
PRAGMA foreign_key_list(binary_id_posts) []
11:48:14.736 [info] QUERY OK db=0.1ms
PRAGMA index_list(binary_id_posts) []
11:48:14.736 [info] QUERY OK db=0.0ms
PRAGMA index_info(binary_id_posts_user_id_index) []
11:48:14.736 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_binary_id_posts_1) []
11:48:14.736 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["binary_id_posts"]
11:48:14.737 [info] QUERY OK db=0.0ms
PRAGMA table_info(binary_id_posts) []
11:48:14.737 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_posts"]
11:48:14.737 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_posts"]
11:48:14.737 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_posts"]
11:48:14.737 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_posts"]
11:48:14.737 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_posts"]
11:48:14.737 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["binary_id_posts"]
11:48:14.742 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(uuid_organizations) []
11:48:14.742 [info] QUERY OK db=0.0ms
PRAGMA index_list(uuid_organizations) []
11:48:14.742 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_uuid_organizations_1) []
11:48:14.742 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["uuid_organizations"]
11:48:14.742 [info] QUERY OK db=0.0ms
PRAGMA table_info(uuid_organizations) []
11:48:14.742 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_organizations"]
11:48:14.742 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_organizations"]
11:48:14.742 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_organizations"]
11:48:14.742 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_organizations"]
11:48:14.742 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(uuid_users) []
11:48:14.742 [info] QUERY OK db=0.0ms
PRAGMA index_list(uuid_users) []
11:48:14.743 [info] QUERY OK db=0.1ms
PRAGMA index_info(uuid_users_organization_id_index) []
11:48:14.743 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_uuid_users_1) []
11:48:14.743 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["uuid_users"]
11:48:14.743 [info] QUERY OK db=0.0ms
PRAGMA table_info(uuid_users) []
11:48:14.743 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_users"]
11:48:14.743 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_users"]
11:48:14.743 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_users"]
11:48:14.743 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_users"]
11:48:14.743 [info] QUERY OK db=0.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_users"]
11:48:14.743 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_users"]
11:48:14.743 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(uuid_posts) []
11:48:14.744 [info] QUERY OK db=0.0ms
PRAGMA index_list(uuid_posts) []
11:48:14.744 [info] QUERY OK db=0.0ms
PRAGMA index_info(uuid_posts_user_id_index) []
11:48:14.744 [info] QUERY OK db=0.0ms
PRAGMA index_info(sqlite_autoindex_uuid_posts_1) []
11:48:14.744 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["uuid_posts"]
11:48:14.744 [info] QUERY OK db=0.0ms
PRAGMA table_info(uuid_posts) []
11:48:14.744 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_posts"]
11:48:14.744 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_posts"]
11:48:14.744 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_posts"]
11:48:14.744 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_posts"]
11:48:14.744 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_posts"]
11:48:14.746 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["uuid_posts"]
11:48:14.746 [info] QUERY OK db=0.0ms
PRAGMA foreign_key_list(proper_date_types) []
11:48:14.747 [info] QUERY OK db=0.0ms
PRAGMA index_list(proper_date_types) []
11:48:14.747 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master WHERE type='table' AND name=? ["proper_date_types"]
11:48:14.747 [info] QUERY OK db=0.0ms
PRAGMA table_info(proper_date_types) []
11:48:14.747 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:48:14.747 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:48:14.747 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:48:14.747 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:48:14.747 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:48:14.747 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:48:14.747 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:48:14.747 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
11:48:14.747 [info] QUERY OK db=0.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["proper_date_types"]
13:39:28.322 [info] QUERY OK db=6.5ms decode=2.2ms queue=6.0ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["users"]
13:39:28.326 [info] QUERY OK db=1.1ms queue=0.9ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["users"]
13:39:28.329 [info] QUERY OK db=2.1ms queue=0.5ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["users"]
13:39:28.330 [info] QUERY OK db=0.5ms queue=0.3ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["users"]
13:39:31.132 [info] QUERY OK db=5.5ms queue=0.9ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["users"]
13:39:31.133 [info] QUERY OK db=0.9ms queue=0.4ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["users"]
13:39:31.136 [info] QUERY OK db=1.7ms queue=0.6ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["users"]
13:39:31.137 [info] QUERY OK db=0.5ms queue=0.3ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["users"]
13:39:51.520 [info] QUERY OK db=6.8ms queue=1.3ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["users"]
13:39:51.522 [info] QUERY OK db=1.2ms queue=0.7ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["users"]
13:39:51.524 [info] QUERY OK db=1.8ms queue=0.5ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["users"]
13:39:51.525 [info] QUERY OK db=0.6ms queue=0.3ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["users"]
13:41:28.319 [error] Postgrex.Protocol (#PID<0.354.0>) disconnected: ** (DBConnection.ConnectionError) owner #PID<0.248.0> timed out because it owned the connection for longer than 120000ms (set via the :ownership_timeout option)
13:41:28.889 [info] QUERY OK db=6.5ms queue=4.2ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["users"]
13:41:28.891 [info] QUERY OK db=1.0ms queue=0.6ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["users"]
13:41:28.894 [info] QUERY OK db=2.0ms queue=0.5ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["users"]
13:41:28.895 [info] QUERY OK db=0.5ms queue=0.3ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["users"]
13:43:09.181 [info] QUERY OK db=5.4ms decode=1.9ms queue=5.7ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["users"]
13:43:09.184 [info] QUERY OK db=0.9ms queue=0.5ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["users"]
13:43:09.186 [info] QUERY OK db=1.5ms queue=0.4ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["users"]
13:43:09.187 [info] QUERY OK db=0.5ms queue=0.2ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["users"]
13:43:10.428 [info] QUERY OK db=3.7ms queue=0.5ms
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS referenced_table,
    ccu.column_name AS referenced_column,
    rc.update_rule,
    rc.delete_rule
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_name = $1
ORDER BY tc.constraint_name, kcu.ordinal_position
 ["users"]
13:43:10.429 [info] QUERY OK db=0.8ms queue=0.3ms
SELECT
    i.relname as index_name,
    array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
    ix.indisunique as is_unique,
    am.amname as index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
JOIN pg_am am ON i.relam = am.oid
WHERE t.relname = $1
  AND NOT ix.indisprimary  -- Exclude primary key indices
GROUP BY i.relname, ix.indisunique, am.amname
ORDER BY i.relname
 ["users"]
13:43:10.431 [info] QUERY OK db=1.4ms queue=0.4ms
  SELECT
      a.attname as column_name,
      CASE
          -- PostgreSQL Array Types (internal names start with _)
          WHEN t.typname = '_int2' THEN 'smallint[]'
          WHEN t.typname = '_int4' THEN 'integer[]'
          WHEN t.typname = '_int8' THEN 'bigint[]'
          WHEN t.typname = '_float4' THEN 'real[]'
          WHEN t.typname = '_float8' THEN 'double precision[]'
          WHEN t.typname = '_numeric' THEN 'numeric[]'
          WHEN t.typname = '_bool' THEN 'boolean[]'
          WHEN t.typname = '_text' THEN 'text[]'
          WHEN t.typname = '_varchar' THEN 'character varying[]'
          WHEN t.typname = '_bpchar' THEN 'character[]'
          WHEN t.typname = '_char' THEN 'character[]'
          WHEN t.typname = '_date' THEN 'date[]'
          WHEN t.typname = '_time' THEN 'time without time zone[]'
          WHEN t.typname = '_timetz' THEN 'time with time zone[]'
          WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
          WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
          WHEN t.typname = '_uuid' THEN 'uuid[]'
          WHEN t.typname = '_json' THEN 'json[]'
          WHEN t.typname = '_jsonb' THEN 'jsonb[]'
          -- Standard PostgreSQL types
          WHEN t.typname = 'int2' THEN 'smallint'
          WHEN t.typname = 'int4' THEN 'integer'
          WHEN t.typname = 'int8' THEN 'bigint'
          WHEN t.typname = 'float4' THEN 'real'
          WHEN t.typname = 'float8' THEN 'double precision'
          WHEN t.typname = 'bpchar' THEN 'character'
          WHEN t.typname = 'varchar' THEN 'character varying'
          WHEN t.typname = 'bool' THEN 'boolean'
          -- Keep standard PostgreSQL type names as-is
          ELSE t.typname
      END as data_type,
      CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as nullable,
      pg_get_expr(ad.adbin, ad.adrelid) as column_default,
      CASE
          WHEN pk.attname IS NOT NULL THEN true
          ELSE false
      END as is_primary_key
  FROM pg_attribute a
  JOIN pg_type t ON a.atttypid = t.oid
  JOIN pg_class c ON a.attrelid = c.oid
  JOIN pg_namespace n ON c.relnamespace = n.oid
  LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
  LEFT JOIN (
      SELECT a.attname
      FROM pg_index i
      JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
      JOIN pg_class c ON i.indrelid = c.oid
      WHERE c.relname = $1 AND i.indisprimary
  ) pk ON pk.attname = a.attname
  WHERE c.relname = $1
      AND n.nspname = 'public'
      AND a.attnum > 0
      AND NOT a.attisdropped
  ORDER BY a.attnum
 ["users"]
13:43:10.432 [info] QUERY OK db=0.5ms queue=0.2ms
SELECT
    con.conname as constraint_name,
    pg_get_constraintdef(con.oid) as constraint_definition,
    array_agg(att.attname) as column_names
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
LEFT JOIN pg_attribute att ON att.attrelid = con.conrelid
    AND att.attnum = ANY(con.conkey)
WHERE con.contype = 'c'
    AND rel.relname = $1
    AND nsp.nspname = 'public'
GROUP BY con.conname, con.oid
 ["users"]
13:45:09.180 [error] Postgrex.Protocol (#PID<0.482.0>) disconnected: ** (DBConnection.ConnectionError) owner #PID<0.370.0> timed out because it owned the connection for longer than 120000ms (set via the :ownership_timeout option)
